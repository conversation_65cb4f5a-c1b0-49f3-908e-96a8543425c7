import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Mail,
  Phone,
  User,
  MessageSquare,
  MapPin,
  Loader2,
  CheckCircle,
} from "lucide-react";

import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { sendNotificationEmail } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";

// Form validation schema
const contactFormSchema = z.object({
  firstName: z.string().min(2, "Voornaam moet minimaal 2 karakters bevatten"),
  lastName: z.string().min(2, "Achternaam moet minimaal 2 karakters bevatten"),
  email: z.string().email("Vul een geldig e-mailadres in"),
  phone: z.string().min(10, "Vul een geldig telefoonnummer in"),
  company: z.string().optional(),
  postalCode: z
    .string()
    .regex(
      /^[1-9][0-9]{3} ?[A-Z]{2}$/i,
      "Vul een geldige postcode in (bijv. 1234 AB)"
    ),
  message: z.string().min(10, "Bericht moet minimaal 10 karakters bevatten"),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

interface ContactFormModalProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  packageName?: string;
  packagePrice?: string;
}

const ContactFormModal = ({
  isOpen,
  setIsOpen,
  packageName = "Klusgebied Pakket",
  packagePrice,
}: ContactFormModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const isMobile = useIsMobile();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
  });

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    try {
      // Save to database
      const { error: insertError } = await supabase
        .from("contact_submissions")
        .insert({
          name: `${data.firstName} ${data.lastName}`,
          email: data.email,
          subject: `Interesse in ${packageName}${
            packagePrice ? ` - ${packagePrice}` : ""
          }`,
          message: `
Naam: ${data.firstName} ${data.lastName}
Email: ${data.email}
Telefoon: ${data.phone}
${data.company ? `Bedrijf: ${data.company}` : ""}
Postcode: ${data.postalCode}
Pakket: ${packageName}${packagePrice ? ` (${packagePrice})` : ""}

Bericht:
${data.message}
          `.trim(),
          email_sent: true,
          email_sent_at: new Date().toISOString(),
        });

      if (insertError) {
        throw new Error("Er ging iets mis bij het opslaan van uw bericht.");
      }

      // Send notification email to admin
      await sendNotificationEmail({
        to: ["<EMAIL>"],
        subject: `Nieuwe contactaanvraag - ${packageName}`,
        content: `
          <div style="font-family: Arial, sans-serif; color: #333; max-width: 600px;">
            <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
              Nieuwe Contactaanvraag
            </h2>
            
            <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #1f2937; margin-top: 0;">Pakket Interesse</h3>
              <p style="font-size: 16px; font-weight: bold; color: #2563eb;">
                ${packageName}${packagePrice ? ` - ${packagePrice}` : ""}
              </p>
            </div>

            <div style="margin: 20px 0;">
              <h3 style="color: #1f2937;">Contactgegevens</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; width: 120px;">Naam:</td>
                  <td style="padding: 8px 0;">${data.firstName} ${
          data.lastName
        }</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">Email:</td>
                  <td style="padding: 8px 0;"><a href="mailto:${
                    data.email
                  }" style="color: #2563eb;">${data.email}</a></td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">Telefoon:</td>
                  <td style="padding: 8px 0;"><a href="tel:${
                    data.phone
                  }" style="color: #2563eb;">${data.phone}</a></td>
                </tr>
                ${
                  data.company
                    ? `
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">Bedrijf:</td>
                  <td style="padding: 8px 0;">${data.company}</td>
                </tr>
                `
                    : ""
                }
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">Postcode:</td>
                  <td style="padding: 8px 0;">${data.postalCode}</td>
                </tr>
              </table>
            </div>

            <div style="margin: 20px 0;">
              <h3 style="color: #1f2937;">Bericht</h3>
              <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #2563eb;">
                ${data.message.replace(/\n/g, "<br>")}
              </div>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                <a href="${
                  window.location.origin
                }/beheerder/berichten" style="color: #2563eb;">
                  Bekijk in admin panel →
                </a>
              </p>
            </div>
          </div>
        `,
      });

      // Send confirmation email to user
      await sendNotificationEmail({
        to: [data.email],
        subject: `Bedankt voor uw interesse in ${packageName}`,
        content: `
          <div style="font-family: Arial, sans-serif; color: #333; max-width: 600px;">
            <h2 style="color: #2563eb;">Bedankt voor uw interesse!</h2>
            
            <p>Beste ${data.firstName},</p>
            
            <p>Bedankt voor uw interesse in <strong>${packageName}</strong>. Wij hebben uw bericht ontvangen en nemen binnen 24 uur contact met u op.</p>
            
            <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
              <h3 style="color: #1e40af; margin-top: 0;">Uw aanvraag</h3>
              <p><strong>Pakket:</strong> ${packageName}${
          packagePrice ? ` - ${packagePrice}` : ""
        }</p>
              <p><strong>Contactpersoon:</strong> ${data.firstName} ${
          data.lastName
        }</p>
              <p><strong>Email:</strong> ${data.email}</p>
            </div>
            
            <p>Heeft u nog vragen? Neem gerust contact met ons op via:</p>
            <ul>
              <li>Email: <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a></li>
              <li>Telefoon: <a href="tel:+31123456789" style="color: #2563eb;">+31 123 456 789</a></li>
            </ul>
            
            <p>Met vriendelijke groet,<br>
            Het Klusgebied Team</p>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">
              <p>Dit is een automatisch gegenereerd bericht van Klusgebied.nl</p>
            </div>
          </div>
        `,
      });

      setIsSuccess(true);
      toast({
        title: "Bericht verzonden!",
        description: "We nemen binnen 24 uur contact met u op.",
      });

      // Reset form after 2 seconds and close modal
      setTimeout(() => {
        reset();
        setIsSuccess(false);
        setIsOpen(false);
      }, 2000);
    } catch (error) {
      console.error("Contact form submission failed:", error);
      toast({
        variant: "destructive",
        title: "Er is iets misgegaan",
        description:
          "Het bericht kon niet worden verzonden. Probeer het opnieuw.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setIsOpen(false);
      reset();
      setIsSuccess(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className={`${
          isMobile
            ? "w-[95vw] max-w-[95vw] h-[90vh] max-h-[90vh] overflow-y-auto"
            : "max-w-2xl"
        } p-0`}
      >
        {isSuccess ? (
          // Success State
          <div className="p-8 text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4 tracking-wide">
              Bericht Verzonden!
            </h3>
            <p className="text-lg text-gray-600 leading-relaxed">
              Bedankt voor uw interesse. We nemen binnen 24 uur contact met u
              op.
            </p>
          </div>
        ) : (
          // Form State
          <>
            <DialogHeader className="p-6 pb-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <DialogTitle className="text-2xl font-bold text-gray-900 tracking-wide leading-tight">
                    Neem Contact Op
                  </DialogTitle>
                  <p className="text-base text-gray-600 mt-2 leading-relaxed tracking-wide">
                    Interesse in{" "}
                    <span className="font-semibold text-blue-600">
                      {packageName}
                    </span>
                    ?
                    {packagePrice && (
                      <span className="text-gray-500"> ({packagePrice})</span>
                    )}
                  </p>
                </div>
              </div>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
              {/* Name Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="firstName"
                    className="text-base font-medium text-gray-700 tracking-wide"
                  >
                    <User className="w-4 h-4 inline mr-2" />
                    Voornaam *
                  </Label>
                  <Input
                    id="firstName"
                    {...register("firstName")}
                    placeholder="Uw voornaam"
                    className={`text-base ${
                      errors.firstName ? "border-red-500" : ""
                    }`}
                  />
                  {errors.firstName && (
                    <p className="text-sm text-red-500">
                      {errors.firstName.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="lastName"
                    className="text-base font-medium text-gray-700 tracking-wide"
                  >
                    Achternaam *
                  </Label>
                  <Input
                    id="lastName"
                    {...register("lastName")}
                    placeholder="Uw achternaam"
                    className={`text-base ${
                      errors.lastName ? "border-red-500" : ""
                    }`}
                  />
                  {errors.lastName && (
                    <p className="text-sm text-red-500">
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Contact Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="email"
                    className="text-base font-medium text-gray-700 tracking-wide"
                  >
                    <Mail className="w-4 h-4 inline mr-2" />
                    E-mailadres *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...register("email")}
                    placeholder="<EMAIL>"
                    className={`text-base ${
                      errors.email ? "border-red-500" : ""
                    }`}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="phone"
                    className="text-base font-medium text-gray-700 tracking-wide"
                  >
                    <Phone className="w-4 h-4 inline mr-2" />
                    Telefoonnummer *
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    {...register("phone")}
                    placeholder="06 12345678"
                    className={`text-base ${
                      errors.phone ? "border-red-500" : ""
                    }`}
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-500">
                      {errors.phone.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Company and Postal Code */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="company"
                    className="text-base font-medium text-gray-700 tracking-wide"
                  >
                    Bedrijfsnaam (optioneel)
                  </Label>
                  <Input
                    id="company"
                    {...register("company")}
                    placeholder="Uw bedrijfsnaam"
                    className="text-base"
                  />
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="postalCode"
                    className="text-base font-medium text-gray-700 tracking-wide"
                  >
                    <MapPin className="w-4 h-4 inline mr-2" />
                    Postcode *
                  </Label>
                  <Input
                    id="postalCode"
                    {...register("postalCode")}
                    placeholder="1234 AB"
                    className={`text-base ${
                      errors.postalCode ? "border-red-500" : ""
                    }`}
                  />
                  {errors.postalCode && (
                    <p className="text-sm text-red-500">
                      {errors.postalCode.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Message Field */}
              <div className="space-y-2">
                <Label
                  htmlFor="message"
                  className="text-base font-medium text-gray-700 tracking-wide"
                >
                  <MessageSquare className="w-4 h-4 inline mr-2" />
                  Uw bericht *
                </Label>
                <textarea
                  id="message"
                  {...register("message")}
                  rows={4}
                  placeholder="Vertel ons meer over uw interesse in dit pakket..."
                  className={`w-full px-3 py-2 border rounded-md text-base resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.message ? "border-red-500" : "border-gray-300"
                  }`}
                />
                {errors.message && (
                  <p className="text-sm text-red-500">
                    {errors.message.message}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <div className="pt-4 border-t border-gray-200">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg text-lg transition-all duration-200"
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Verzenden...
                    </span>
                  ) : (
                    "Verstuur Bericht"
                  )}
                </Button>
                <p className="text-sm text-gray-500 text-center mt-3 leading-relaxed">
                  We nemen binnen 24 uur contact met u op
                </p>
              </div>
            </form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ContactFormModal;
