/**
 * @description Package detail page for Klusgebied Partner (€499 per month) subscription plan.
 * Provides comprehensive information about features, benefits, and pricing for craftsmen
 * who want to build their own brand with maximum visibility and regional exclusivity.
 */
import { useState } from "react";
import { Helmet } from "react-helmet-async";
import {
  CheckCircle,
  ArrowRight,
  Crown,
  Users,
  MapPin,
  Mail,
  Video,
  TrendingUp,
  Globe,
  Camera,
  Award,
} from "lucide-react";

import Footer from "@/components/landing/Footer";
import ContactFormModal from "@/components/modal/ContactFormModal";
import usePageTitle from "@/hooks/usePageTitle";

const KlusgebiedPartnerPage = () => {
  usePageTitle("Klusgebied Partner - €499 per maand | Premium Partner Pakket");
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const exclusiveFeatures = [
    {
      icon: <Globe className="w-6 h-6" />,
      title: "Professionele website op maat (binnen 30 dagen live)",
      description:
        "Inclusief homepage, diensten, contact, reviews, en volledig responsive design.",
    },
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Eigen domeinnaam + professioneel e-mailadres",
      description: "Zoals: www.jouwbedrijf.nl + <EMAIL>",
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "1 jaar hosting & technisch onderhoud inbegrepen",
      description: "Wij zorgen dat jouw website snel en veilig blijft.",
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Google Mijn Bedrijf registratie & SEO-optimalisatie",
      description: "Jij wordt ook buiten Klusgebied gevonden via Google.",
    },
    {
      icon: <Camera className="w-6 h-6" />,
      title: "Professionele fotoshoot op locatie",
      description: "Jij en je werk professioneel vastgelegd.",
    },
    {
      icon: <Video className="w-6 h-6" />,
      title: "1 bedrijfsvideo (60 seconden)",
      description:
        "Een krachtige promotievideo voor op je site, socials en Klusgebied-profiel.",
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Maandelijkse advertenties (€100 adbudget inbegrepen)",
      description:
        "Doorlopende zichtbaarheid en bereik via social media campagnes.",
    },
    {
      icon: <Award className="w-6 h-6" />,
      title: "Regio-exclusiviteit (6 maanden gegarandeerd)",
      description: "Jij bent de enige Partner in jouw vakgebied én regio.",
    },
  ];

  const lifetimeFeatures = [
    {
      icon: <Crown className="w-6 h-6" />,
      title: 'Lifetime "Founding Partner" badge',
      description:
        "Permanente erkenning op Klusgebied.nl dat jij onderdeel bent van de eerste lichting vakmannen.",
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Vermelding op de officiële Topvakmannen-pagina",
      description: "Extra vertrouwen en zichtbaarheid voor potentiële klanten.",
    },
  ];

  const includedFromPro = [
    "Topvermelding in 3 regio's",
    "Onbeperkt reageren op klussen",
    "Professioneel ingericht profiel",
    "1-op-1 groeigesprek per kwartaal + op aanvraag",
    "Professionele profielvideo (1 minuut)",
    "Toegang tot de Klusgebied Community",
    "Social media shout-out (1x per maand)",
    "Vermelding in nieuwsbrief (doorlopend, min. 6x per jaar)",
  ];

  const handleGetStarted = () => {
    setIsContactModalOpen(true);
  };

  const footerProps = {
    logoSrc:
      "https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png",
    brandName: "Klusgebied",
    description:
      "Vind snel en eenvoudig een geverifieerde vakman in jouw regio voor elke klus.",
    contact: {
      address: "Slotermeerlaan 58, 1064 HC Amsterdam",
      phone: "+31 123 456 789",
      email: "<EMAIL>",
    },
    links: {
      "Voor Klanten": [
        { label: "Vind een Vakman", href: "/diensten" },
        { label: "Klantenservice", href: "/klantenservice" },
        { label: "Garantie", href: "/garantie" },
        { label: "Veelgestelde Vragen", href: "/faq" },
      ],
      "Voor Vakmensen": [
        { label: "Aanmelden als Vakman", href: "/vakman" },
        { label: "Voordelen", href: "/vakman" },
        { label: "Investeer in Klusgebied+", href: "/investeren" },
        { label: "Support", href: "/support" },
      ],
    },
    socials: [
      { name: "Facebook", href: "#" },
      { name: "Twitter", href: "https://x.com/heybossAI" },
      { name: "Instagram", href: "#" },
      {
        name: "LinkedIn",
        href: "https://www.linkedin.com/company/heyboss-xyz/",
      },
    ],
    legal: {
      privacyPolicyUrl: "https://legal.heyboss.tech/67845a5e6e6bf5ecd4a3ae47/",
      termsAndConditionsUrl:
        "https://legal.heyboss.tech/67845cfe76f9675292514b80/",
      year: 2025,
    },
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      <Helmet>
        <title>
          Klusgebied Partner - €499 per maand | Premium Partner Pakket
        </title>
        <meta
          name="description"
          content="Klusgebied Partner voor €499 per maand. Voor vakmannen die een eigen merk willen bouwen met maximale zichtbaarheid en regionale exclusiviteit. Eigen website, fotoshoot en meer."
        />
        <meta
          property="og:title"
          content="Klusgebied Partner - €499 per maand | Premium Partner Pakket"
        />
        <meta
          property="og:description"
          content="Klusgebied Partner voor €499 per maand. Voor vakmannen die een eigen merk willen bouwen met maximale zichtbaarheid en regionale exclusiviteit."
        />
        <meta
          property="og:image"
          content="https://heyboss.heeyo.ai/user-assets/7265ee1d-7ceb-41ac-a6af-bc9499143883_3xnKAaGW.png"
        />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>

      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-purple-50 to-purple-100 py-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-purple-500 rounded-full mb-8">
                <Crown className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-5xl font-bold text-gray-900 mb-6 tracking-wide leading-tight">
                Klusgebied Partner
              </h1>
              <div className="text-4xl font-bold text-purple-600 mb-4">
                €499 per maand{" "}
                <span className="text-lg text-gray-600 font-normal">
                  (excl. btw)
                </span>
              </div>
              <p className="text-xl text-gray-700 mb-8 leading-relaxed tracking-wide">
                Voor vakmannen die een eigen merk willen bouwen met maximale
                zichtbaarheid en regionale exclusiviteit.
              </p>
              <button
                onClick={handleGetStarted}
                className="bg-purple-500 hover:bg-purple-600 text-white font-semibold px-8 py-4 rounded-lg text-lg transition-all duration-200 flex items-center justify-center mx-auto group"
              >
                Start Nu
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </section>

        {/* Included from Pro */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Alles van Klusgebied Pro, plus:
              </h2>
              <div className="grid md:grid-cols-2 gap-4 mb-12">
                {includedFromPro.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-teal-500 mr-3 flex-shrink-0" />
                    <span className="text-base text-gray-700 tracking-wide leading-relaxed">
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Exclusive Partner Features */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-16 tracking-wide leading-relaxed">
                Exclusieve Partner Voordelen
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                {exclusiveFeatures.map((feature, index) => (
                  <div
                    key={index}
                    className="bg-white p-8 rounded-xl border border-gray-200 hover:shadow-lg transition-shadow"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-6">
                      <div className="text-purple-600">{feature.icon}</div>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3 tracking-wide leading-relaxed">
                      {feature.title}
                    </h3>
                    <p className="text-base text-gray-600 leading-relaxed tracking-wide">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Lifetime Benefits */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-16 tracking-wide leading-relaxed">
                Lifetime Voordelen
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                {lifetimeFeatures.map((feature, index) => (
                  <div
                    key={index}
                    className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-xl border border-purple-200"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-purple-500 rounded-lg mb-6">
                      <div className="text-white">{feature.icon}</div>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3 tracking-wide leading-relaxed">
                      {feature.title}
                    </h3>
                    <p className="text-base text-gray-600 leading-relaxed tracking-wide">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Support Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Persoonlijke Accountmanager
              </h2>
              <div className="bg-purple-50 border border-purple-200 rounded-xl p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mx-auto mb-6">
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 tracking-wide">
                  Dedicated ondersteuning
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed tracking-wide">
                  Als Partner krijg je een persoonlijke accountmanager die je
                  helpt met strategie, groei en optimalisatie van je
                  aanwezigheid.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contract Details */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 text-center mb-12 tracking-wide leading-relaxed">
                Contract Details
              </h2>
              <div className="bg-purple-50 border border-purple-200 rounded-xl p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">
                      Looptijd
                    </h3>
                    <p className="text-base text-gray-700 leading-relaxed tracking-wide">
                      Minimaal 6 maanden, daarna per maand opzegbaar
                    </p>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-4 tracking-wide">
                      Bonus
                    </h3>
                    <p className="text-base text-gray-700 leading-relaxed tracking-wide">
                      Betaal 12 maanden vooruit en ontvang 2 maanden gratis +
                      gratis digitale bedrijfsbrochure (PDF)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-br from-purple-500 to-purple-600">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-4xl font-bold text-white mb-6 tracking-wide leading-tight">
                Klaar om Partner te worden?
              </h2>
              <p className="text-xl text-purple-100 mb-8 leading-relaxed tracking-wide">
                Bouw je eigen merk, krijg exclusiviteit in je regio en
                maximaliseer je zichtbaarheid met Klusgebied Partner.
              </p>
              <button
                onClick={handleGetStarted}
                className="bg-white text-purple-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-lg text-lg transition-all duration-200 flex items-center justify-center mx-auto group"
              >
                Neem Contact Op
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </section>
      </main>

      <Footer {...footerProps} />

      <ContactFormModal
        isOpen={isContactModalOpen}
        setIsOpen={setIsContactModalOpen}
        packageName="Klusgebied Partner"
        packagePrice="€499 per maand"
      />
    </div>
  );
};

export default KlusgebiedPartnerPage;
